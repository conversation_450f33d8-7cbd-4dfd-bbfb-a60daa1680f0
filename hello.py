#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from datetime import datetime

print("Hello, World!")
current_time = datetime.now()
print(f"Data e hora atual: {current_time.strftime('%d/%m/%Y %H:%M:%S')}")

print("\nChoose an option:")
print("1 - Show only time")
print("2 - Show only date")
print("3 - Show date and time")

while True:
    try:
        opcao = input("Enter your choice (1-3): ").strip()
        
        if opcao in ["1", "2", "3"]:
            if opcao == "1":
                print(f"Current time: {current_time.strftime('%H:%M:%S')}")
            elif opcao == "2":
                print(f"Current date: {current_time.strftime('%d/%m/%Y')}")
            else:  # opcao == "3"
                print(f"Current date and time: {current_time.strftime('%d/%m/%Y %H:%M:%S')}")
            break
        else:
            print(f"Invalid option: '{opcao}'. Please choose 1, 2, or 3.")
            continue
    except EOFError:
        print("\nNo input provided. Showing default date and time.")
        break
    print(f"Current date and time: {current_time.strftime('%d/%m/%Y %H:%M:%S')}")