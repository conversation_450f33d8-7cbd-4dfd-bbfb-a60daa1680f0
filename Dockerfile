# Use the official Python runtime as a parent image (updated to latest stable)
FROM python:3.12-slim

# Set timezone to Brazil (GMT-3)
ENV TZ=America/Sao_Paulo
RUN apt-get update && apt-get install -y tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt /app/

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY . /app

# Create a non-root user for security
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app
USER appuser

# Make port 80 available to the world outside this container
# (uncomment if your app needs to expose a port)
# EXPOSE 80

# Define environment variable
ENV NAME=World

# Run hello.py when the container launches
CMD ["python", "hello.py"]
